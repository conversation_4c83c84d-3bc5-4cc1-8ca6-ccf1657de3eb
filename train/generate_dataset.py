import json
import random
from pathlib import Path

from PIL import Image

IMG_SOURCE_FOLDER = "collect41"  # 原始图片存放目录
ANNOTATION_JSON_FILE = "sp_di_page_download.json"  # 标注信息JSON文件
OUTPUT_DATASET_FOLDER = "sp_dataset"  # YOLOv5 数据集输出目录
TRAIN_TEST_SPLIT_RATIO = 0.8  # 训练集所占比例 (例如 0.8 表示 80% 训练集, 20% 测试集)


def generate_yolo_dataset(
    img_source_dir: Path,
    json_annotation_path: Path,
    output_dir: Path,
    train_ratio: float = 0.8,
):
    """
    从原始图片和JSON标注文件生成YOLOv5格式的数据集。

    Args:
        img_source_dir (Path): 包含原始图片的目录路径。
        json_annotation_path (Path): 包含标注信息的JSON文件路径。
        output_dir (Path): 生成YOLOv5数据集的根目录路径。
        train_ratio (float): 训练集所占的比例，介于0和1之间。
    """
    if not img_source_dir.is_dir():
        print(f"错误: 图片源目录 '{img_source_dir}' 不存在或不是一个目录。")
        return

    if not json_annotation_path.is_file():
        print(f"错误: 标注JSON文件 '{json_annotation_path}' 不存在。")
        return

    print(f"开始处理图片源目录: {img_source_dir}")
    print(f"使用标注文件: {json_annotation_path}")
    print(f"数据集将输出到: {output_dir}")

    # 加载标注数据
    try:
        with open(json_annotation_path, "r", encoding="utf-8") as f:
            annotations_data = json.load(f)
        print(f"成功加载 {len(annotations_data)} 条标注数据。")
    except json.JSONDecodeError:
        print(f"错误: 解析JSON文件 '{json_annotation_path}' 失败。")
        return
    except Exception as e:
        print(f"错误: 读取JSON文件 '{json_annotation_path}' 时发生错误: {e}")
        return

    image_files = list(img_source_dir.glob("*[.jpg|.jpeg|.png]"))
    if not image_files:
        print(f"警告: 在 '{img_source_dir}' 中未找到任何图片文件。")
        return

    print(f"在源目录中找到 {len(image_files)} 张图片。")

    # 创建输出目录结构
    images_train_path = output_dir / "images" / "train"
    labels_train_path = output_dir / "labels" / "train"
    images_test_path = output_dir / "images" / "test"
    labels_test_path = output_dir / "labels" / "test"

    images_train_path.mkdir(parents=True, exist_ok=True)
    labels_train_path.mkdir(parents=True, exist_ok=True)
    images_test_path.mkdir(parents=True, exist_ok=True)
    labels_test_path.mkdir(parents=True, exist_ok=True)

    processed_count = 0
    skipped_count = 0

    for img_path in image_files:
        img_filename = img_path.name

        annotation = annotations_data.get(img_filename)
        if not annotation:
            # print(f"警告: 图片 '{img_filename}' 在JSON文件中没有找到对应的标注，已跳过。")
            skipped_count += 1
            continue

        rects = annotation.get("rect")
        if not rects:  # rects 可能为 None 或空列表
            # print(f"警告: 图片 '{img_filename}' 的标注中没有 'rect' 信息或 'rect' 为空，已跳过。")
            skipped_count += 1
            continue

        # 检查第一个 bounding box 的宽度和高度是否为0 (原始逻辑)
        # 注意：如果 rects 为空，这里会报错，所以上面加了 not rects 的判断
        if rects[0][2] == 0 or rects[0][3] == 0:
            # print(f"警告: 图片 '{img_filename}' 的第一个标注框宽度或高度为0，已跳过。")
            skipped_count += 1
            continue

        try:
            with Image.open(img_path) as im:
                # 决定是放入训练集还是测试集
                # random.random() 生成 [0.0, 1.0) 之间的随机浮点数
                if random.random() < train_ratio:
                    dataset_split_folder = "train"
                    current_images_path = images_train_path
                    current_labels_path = labels_train_path
                else:
                    dataset_split_folder = "test"
                    current_images_path = images_test_path
                    current_labels_path = labels_test_path

                # 处理图片格式 (png -> jpg)
                output_img_filename = img_filename
                if img_path.suffix.lower() == ".png":
                    im = im.convert("RGB")
                    output_img_filename = img_path.with_suffix(".jpg").name

                # 保存图片
                output_image_filepath = current_images_path / output_img_filename
                im.save(
                    output_image_filepath
                )  # , quality=95) # 可以根据需要调整图片质量

                # 创建并保存 YOLO 格式的标签文件
                label_filename = Path(output_img_filename).with_suffix(".txt").name
                label_filepath = current_labels_path / label_filename

                with open(label_filepath, "w", encoding="utf-8") as f_label:
                    for r_data in rects:
                        # 假设 r_data 已经是 [x_center_norm, y_center_norm, width_norm, height_norm]
                        # 类别索引默认为 0
                        class_index = 0
                        x_center, y_center, width, height = (
                            r_data[0],
                            r_data[1],
                            r_data[2],
                            r_data[3],
                        )
                        f_label.write(
                            f"{class_index} {x_center} {y_center} {width} {height}\n"
                        )

                processed_count += 1

        except FileNotFoundError:
            print(
                f"错误: 图片文件 '{img_path}' 未找到 (可能在处理过程中被移动或删除)。"
            )
            skipped_count += 1
        except Exception as e:
            print(f"处理图片 '{img_filename}' 时发生错误: {e}")
            skipped_count += 1

    print("\n处理完成。")
    print(f"成功处理并保存了 {processed_count} 张图片及其标注。")
    print(f"跳过了 {skipped_count} 张图片（因无标注、标注无效或处理错误）。")
    print(f"数据集已生成在: {output_dir.resolve()}")


if __name__ == "__main__":
    img_source_path = Path(IMG_SOURCE_FOLDER)
    json_file_path = Path(ANNOTATION_JSON_FILE)
    output_dataset_path = Path(OUTPUT_DATASET_FOLDER)

    generate_yolo_dataset(
        img_source_path, json_file_path, output_dataset_path, TRAIN_TEST_SPLIT_RATIO
    )
    print("========== 脚本执行完毕 ==========")
