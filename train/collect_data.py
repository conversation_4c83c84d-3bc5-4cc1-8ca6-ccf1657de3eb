import json
import random
import time
from pathlib import Path
from typing import Any, Dict, List, Tuple

import requests

SOURCE_JSON_DIR = Path("./jsons_collect.4.1/")  # 包含源JSON文件的目录
OUTPUT_IMAGE_FOLDER = Path("collect41")  # 下载图片的输出目录
OUTPUT_MANIFEST_JSON = Path("di_page.json")  # 输出的包含图片信息和原始坐标的JSON文件名

# 网络请求相关设置
REQUEST_TIMEOUT = 30  # 请求超时时间 (秒)
MIN_SLEEP_TIME = 1.0  # 下载间最小随机延时 (秒)
MAX_SLEEP_TIME = 3.0  # 下载间最大随机延时 (秒)
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"


def download_image(
    session: requests.Session, url: str, save_path: Path, js_filename: str
) -> bool:
    """
    下载单个图片。

    Args:
        session: requests的Session对象。
        url: 图片的URL。
        save_path: 图片保存的完整路径。
        js_filename: 来源的JSON文件名，用于记录错误。

    Returns:
        bool: 下载是否成功。
    """
    if save_path.exists():
        # print(f"图片已存在: {save_path}")
        return True
    try:
        headers = {"User-Agent": USER_AGENT}
        response = session.get(
            url, timeout=REQUEST_TIMEOUT, headers=headers, stream=True
        )
        response.raise_for_status()  # 如果请求失败 (状态码 4xx or 5xx), 则抛出异常

        with open(save_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        # print(f"成功下载: {url} -> {save_path}")
        return True
    except requests.exceptions.RequestException as e:
        print(f"下载错误 (URL: {url}, 来源JS: {js_filename}): {e}")
        return False
    except IOError as e:
        print(f"保存文件错误 (路径: {save_path}, 来源JS: {js_filename}): {e}")
        return False


def collect_data(
    source_json_dir: Path, output_image_dir: Path, output_manifest_path: Path
) -> Tuple[Dict[str, Any], List[str]]:
    """
    从源JSON文件收集图片URL和相关元数据，下载图片，并生成清单JSON文件。

    Args:
        source_json_dir (Path): 包含源JSON文件的目录。
        output_image_dir (Path): 下载图片的输出目录。
        output_manifest_path (Path): 生成的清单JSON文件路径。

    Returns:
        Tuple[Dict[str, Any], List[str]]: (生成的页面数据字典, 下载失败的URL列表)
    """
    if not source_json_dir.is_dir():
        print(f"错误: 源JSON目录 '{source_json_dir}' 不存在。")
        return {}, []

    output_image_dir.mkdir(parents=True, exist_ok=True)

    di_page: Dict[str, Any] = {}  # 用于存储页面信息和原始坐标
    download_errors_list: List[str] = []  # 用于记录下载失败的URL

    source_json_files = list(source_json_dir.glob("*.json"))
    if not source_json_files:
        print(f"警告: 在 '{source_json_dir}' 中未找到任何JSON文件。")
        return {}, []

    print(f"找到 {len(source_json_files)} 个源JSON文件进行处理...")

    for json_file_path in source_json_files:
        print(f"处理JSON文件: {json_file_path.name}")
        try:
            with open(json_file_path, "r", encoding="utf-8") as f:
                json_text = json.load(f)
        except Exception as e:
            print(f"读取或解析JSON文件 '{json_file_path.name}' 失败: {e}")
            continue

        paper_id = json_text["data"].get("paper_id", "unknown_paper")
        page_list = json_text["data"].get("page_list", [])
        # 注意：items_answer 似乎是针对整个JSON文件的，而不是每一页。
        # 如果items_answer的坐标是相对于特定页面的，这里的逻辑可能需要调整，
        # 或者源JSON的结构需要更清晰地表明这种关联。
        # 当前脚本将把 'items_answer' 中的所有 'rect' 与该JSON文件中的每一页关联。
        items_answer = json_text["data"].get("items_answer", [])

        current_file_rects = []
        for item in items_answer:
            if item.get("item_type") == "rect" and all(
                k in item for k in ["x", "y", "w", "h"]
            ):
                current_file_rects.append([item["x"], item["y"], item["w"], item["h"]])
            # else:
            # print(f"警告: 在 {json_file_path.name} 中找到一个 'item_answer' 不是 'rect' 类型或缺少坐标: {item.get('item_type')}")

        if not page_list:
            print(
                f"警告: JSON文件 '{json_file_path.name}' 中 'page_list' 为空或不存在。"
            )
            continue

        for page_data in page_list:
            page_url = page_data.get("page_url")
            page_idx = page_data.get("page_idx", "unknown_idx")

            if not page_url:
                print(f"警告: 在 '{json_file_path.name}' 的一页中缺少 'page_url'。")
                continue

            try:
                file_extension = page_url.split(".")[-1]
                if not file_extension or len(file_extension) > 4:  # 基本的扩展名检查
                    file_extension = "jpg"  # 默认扩展名
            except IndexError:
                file_extension = "jpg"

            # 构建独特的文件名 fn
            # stem 是源JSON文件的名称（不含扩展名）
            fn = f"{json_file_path.stem}_{paper_id}_{page_idx}.{file_extension}"

            # 使用 fn 作为 di_page 中的键
            if fn not in di_page:
                di_page[fn] = {}

            di_page[fn]["page_index"] = page_idx
            di_page[fn]["original_url"] = page_url
            di_page[fn]["source_json"] = json_file_path.name
            # 将从整个 items_answer 中提取的 rects 关联到此页面
            # 如果 rects 应该是页面特定的，源JSON结构或此逻辑需要修改
            di_page[fn]["rect"] = current_file_rects  # 存储原始坐标
            # print(f"为 {fn} 设置了 {len(current_file_rects)} 个rects。")

    # 保存包含所有页面信息和原始坐标的JSON清单文件
    try:
        with open(output_manifest_path, "w", encoding="utf-8") as f:
            json.dump(di_page, f, ensure_ascii=False, indent=4)
        print(f"\n清单文件已保存到: {output_manifest_path.resolve()}")
        print(f"共收集到 {len(di_page)} 条页面记录。")
    except Exception as e:
        print(f"错误: 保存清单JSON文件 '{output_manifest_path}' 失败: {e}")
        return di_page, download_errors_list  # 仍然返回已处理的 di_page

    # 下载图片
    print("\n开始下载图片...")
    downloaded_count = 0
    failed_count = 0
    with requests.Session() as session:
        for i, (img_filename, data) in enumerate(di_page.items()):
            url = data.get("original_url")
            if not url:
                print(f"警告: 图片 '{img_filename}' 缺少 'original_url'，无法下载。")
                download_errors_list.append(f"Missing URL for {img_filename}")
                failed_count += 1
                continue

            image_save_path = output_image_dir / img_filename
            print(f"下载中 ({i + 1}/{len(di_page)}): {img_filename} 从 {url}")
            if download_image(
                session,
                url,
                image_save_path,
                data.get("source_json", "unknown_source.json"),
            ):
                downloaded_count += 1
            else:
                download_errors_list.append(url)  # 记录失败的URL
                failed_count += 1

            # 随机延时
            time.sleep(random.uniform(MIN_SLEEP_TIME, MAX_SLEEP_TIME))

    print("\n图片下载完成。")
    print(f"成功下载: {downloaded_count} 张图片。")
    print(f"下载失败: {failed_count} 张图片。")
    if download_errors_list:
        print("下载失败的URL/项目:")
        for error_item in download_errors_list:
            print(f"  - {error_item}")

    return di_page, download_errors_list


if __name__ == "__main__":
    print("开始执行数据收集脚本...")
    # 确保配置的目录存在
    SOURCE_JSON_DIR.mkdir(parents=True, exist_ok=True)

    collected_pages, errors = collect_data(
        SOURCE_JSON_DIR, OUTPUT_IMAGE_FOLDER, OUTPUT_MANIFEST_JSON
    )

    print("\n--- 数据收集脚本执行完毕 ---")
    print(f"最终清单中包含 {len(collected_pages)} 条记录。")
    if errors:
        print(f"共发生 {len(errors)} 次下载错误。")

    print("\n重要提示:")
    print(f"1. 图片已下载到: {OUTPUT_IMAGE_FOLDER.resolve()}")
    print(f"2. 包含原始坐标的清单文件已生成: {OUTPUT_MANIFEST_JSON.resolve()}")
    print("3. YOLOv5 需要归一化的 [x_center, y_center, width, height] 格式。")
