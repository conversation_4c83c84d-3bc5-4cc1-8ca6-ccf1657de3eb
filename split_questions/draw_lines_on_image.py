import argparse
import json
import os
from urllib.parse import urlparse

import requests
from PIL import Image, ImageDraw


def draw_lines_on_image(image_path_or_url, export_folder, coordinates_json):
    """
    在图片上根据归一化坐标绘制横线和竖线，并保存到指定文件夹。

    参数:
    image_path_or_url (str): 图片的本地路径或URL。
    export_folder (str): 导出的图片副本存放的文件夹路径。
    coordinates_json (str): 包含归一化坐标的JSON字符串。
    """

    try:
        # 1. 解析JSON字符串
        try:
            coordinates = json.loads(coordinates_json)
            heights = coordinates.get("height", [])
            columns = coordinates.get("column", [])
        except json.JSONDecodeError:
            print("错误：无效的JSON字符串。")
            return

        # 2. 加载图片
        print(f"正在加载图片: {image_path_or_url}...")
        if image_path_or_url.startswith("http://") or image_path_or_url.startswith(
            "https://"
        ):
            try:
                response = requests.get(image_path_or_url, stream=True)
                response.raise_for_status()  # 如果请求失败则引发HTTPError
                img = Image.open(response.raw)
            except requests.exceptions.RequestException as e:
                print(f"错误：无法从URL加载图片: {e}")
                return
        else:
            try:
                img = Image.open(image_path_or_url)
            except FileNotFoundError:
                print(f"错误：图片文件未找到: {image_path_or_url}")
                return
            except IOError:
                print(f"错误：无法打开图片文件: {image_path_or_url}")
                return

        img_width, img_height = img.size
        draw = ImageDraw.Draw(img)

        # 定义线条颜色 (蓝色 BGR)
        line_color = (0, 0, 255)  # RGB for Pillow
        line_width = 2  # 可以根据需要调整线条宽度

        # 3. 根据归一化坐标绘制横线
        for norm_y in heights:
            actual_y = int(norm_y * img_height)
            draw.line(
                [(0, actual_y), (img_width, actual_y)],
                fill=line_color,
                width=line_width,
            )
            print(f"绘制横线于 y={actual_y} (归一化: {norm_y:.4f})")

        # 4. 根据归一化坐标绘制竖线
        for norm_x in columns:
            actual_x = int(norm_x * img_width)
            draw.line(
                [(actual_x, 0), (actual_x, img_height)],
                fill=line_color,
                width=line_width,
            )
            print(f"绘制竖线于 x={actual_x} (归一化: {norm_x:.4f})")

        # 5. 导出新的图片副本
        if not os.path.exists(export_folder):
            try:
                os.makedirs(export_folder)
                print(f"创建导出文件夹: {export_folder}")
            except OSError as e:
                print(f"错误：无法创建导出文件夹 '{export_folder}': {e}")
                return

        # 生成输出文件名
        if image_path_or_url.startswith("http://") or image_path_or_url.startswith(
            "https://"
        ):
            parsed_url = urlparse(image_path_or_url)
            base_name = os.path.basename(parsed_url.path)
        else:
            base_name = os.path.basename(image_path_or_url)

        name, ext = os.path.splitext(base_name)
        output_filename = f"{name}_drawn{ext}"
        output_path = os.path.join(export_folder, output_filename)

        try:
            img.save(output_path)
            print(f"成功将带线条的图片保存到: {output_path}")
        except IOError as e:
            print(f"错误：无法保存图片到 '{output_path}': {e}")
        except SystemError as e:
            print(f"错误：保存图片时发生系统错误 (可能是图片过大或格式问题): {e}")

    except Exception as e:
        print(f"发生未知错误: {e}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="在图片上根据JSON坐标绘制线条并导出。")
    parser.add_argument("json_string", type=str, help="包含归一化坐标的JSON字符串。")
    parser.add_argument("image_url_or_path", type=str, help="图片的URL或本地文件路径。")
    parser.add_argument("export_folder", type=str, help="导出图片的文件夹路径。")

    args = parser.parse_args()

    draw_lines_on_image(args.image_url_or_path, args.export_folder, args.json_string)
