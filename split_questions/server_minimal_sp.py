from io import BytesIO

import numpy as np
import requests
import torch
import uvicorn
from fastapi import FastAPI
from PIL import Image

app = FastAPI()

model_ready = {}

box_coordinate_keys = ["x1", "y1", "x2", "y2"]

MAX_INIT_BOUNDARY = 99999999

MIN_INIT_BOUNDARY = -1


# cuttype没有精确管理，单页双栏 vs 双页单栏，默认双页单栏
def merge_close_coordinates(height_list, threshold):
    """
    合并相近的坐标点
    """

    remove_list = []
    for idx in range(len(height_list) - 1):
        if height_list[idx + 1] - height_list[idx] < threshold and int(
            height_list[idx + 1]
        ) == int(height_list[idx]):
            remove_list.append(height_list[idx + 1])

    for idx in remove_list:
        height_list.remove(idx)

    for idx in range(len(height_list)):
        dif = height_list[idx] - round(height_list[idx])
        if 0 <= dif < 0.01:
            height_list[idx] = round(height_list[idx]) + 0.01
        if 0 > dif > -0.01:
            height_list[idx] = round(height_list[idx]) - 0.01

    return height_list


def detect_image_box_intersection(img, boxes):
    """
    检测图像与边界框的交互
    """

    # 灰度阈值
    gray_threshold = 50
    # 交叉阈值
    intersection_threshold = 0.1
    w, h = img.shape

    # 检测到的交叉点列表
    intersection_points = []
    for i in range(len(boxes)):
        y1 = int(h * boxes[i]["y1"])
        y2 = int(h * boxes[i]["y2"])
        y = [y1, y2]
        # 交叉计数
        intersection_count = np.zeros_like(y)

        for j in range(len(y)):
            for k in range(boxes[i]["x1"], boxes[i]["x2"]):
                x = int(w * k)
                if img[x][y[j]] < gray_threshold:
                    intersection_count[j] += 1

            if (
                intersection_count[j] / ((boxes[i]["x2"] - boxes[i]["x1"]) * w)
                > intersection_threshold
            ):
                # 当前检测到的交叉点的y坐标值
                intersection_y = 0
                if j == 0:
                    intersection_y = boxes[i]["y1"]
                if j == 1:
                    intersection_y = boxes[i]["y2"]
                intersection_points.append(intersection_y + boxes[i]["lb"])

    return intersection_points


def detect_columns(boxes, width_threshold=0.1):
    """
    检测页面内的列（columns）并返回相关信息

    Args:
        boxes: 边界框列表
        width_threshold: 宽度阈值，用于判断是否为新列

    Returns:
        column_count: 列数
        column_first_indices: 每列第一个框的索引
        left_edges: 所有边界框的左边缘坐标（已排序）
        layout_result: 包含cutType的结果字典
    """

    # 所有边界框的左边缘坐标
    left_edges = []
    for idx in boxes:
        left_edges.append(idx["x1"])
    left_edges.sort()

    column_count = 1
    # 每列第一个框的索引
    column_first_indices = [0]

    for idx in range(1, len(left_edges)):
        if left_edges[idx] - left_edges[column_first_indices[-1]] < width_threshold:
            continue
        column_count += 1
        column_first_indices.append(idx)

    if column_count > 3:
        print("error: colums > 3")
        return None, None, None, None

    layout_result = {}
    if column_count == 1:
        layout_result["cutType"] = 1
    elif column_count == 2:
        layout_result["cutType"] = 2
    else:
        layout_result["cutType"] = 3

    # 分栏时，如果最后一题框的宽度大于单栏题框长的1.5倍，则删除最后一题框，此次为模型错判题框的容错处理
    if layout_result["cutType"] == 2:
        if (
            left_edges[column_first_indices[1]] - left_edges[column_first_indices[0]]
        ) * 1.5 < (boxes[-1]["x2"] - boxes[-1]["x1"]):
            print("进入删除最后一题框")
            del boxes[-1]
            left_edges = []
            for idx in boxes:
                left_edges.append(idx["x1"])
            left_edges.sort()
            column_count = 1
            column_first_indices = [0]
            for idx in range(1, len(left_edges)):
                if (
                    left_edges[idx] - left_edges[column_first_indices[-1]]
                    < width_threshold
                ):
                    continue
                column_count += 1
                column_first_indices.append(idx)

    return column_count, column_first_indices, left_edges, layout_result


def run(url):
    empty_result = {"column": [], "height": [], "cutType": 0}
    global model_ready

    if model_ready == {}:
        loadmodel()
    model = model_ready["sp"]

    try:
        r = requests.get(url)
        im = Image.open(BytesIO(r.content))
    except Exception as e:
        print(f"read {url} error: ", e)
        empty_result["code"] = 21003
        return empty_result

    width = 640
    height = int(im.size[1] / im.size[0] * width)
    im = im.resize((width, height))
    results = model(im)

    boxes = results_to_json(results, [width, height])

    if len(boxes) == 0:
        print("error: zero box")
        return empty_result

    width_threshold = 0.1
    height_threshold = 1 / 40.0

    for _ in range(2):
        # 边界框宽度列表
        box_widths = []

        for idx in range(len(boxes)):
            box_widths.append(boxes[idx]["x2"] - boxes[idx]["x1"])

        sorted_box_widths = box_widths.copy()
        sorted_box_widths.sort()
        delete_w = 0

        for idx in sorted_box_widths:
            if (
                idx < sorted_box_widths[int(len(sorted_box_widths) * 0.4)] * 0.7
                or idx < sorted_box_widths[int(len(sorted_box_widths) * 0.1)] * 0.9
            ):
                delete_w = idx
            else:
                break

        remove_list = []
        # 有效边界框的边界
        valid_box_boundaries = [
            MAX_INIT_BOUNDARY,
            MIN_INIT_BOUNDARY,
        ]  # correct box border in this section
        for idx in range(len(boxes)):
            if boxes[idx]["x2"] - boxes[idx]["x1"] <= delete_w:
                remove_list.append(boxes[idx])
            else:
                valid_box_boundaries[0] = min(valid_box_boundaries[0], boxes[idx]["x1"])
                valid_box_boundaries[1] = max(valid_box_boundaries[1], boxes[idx]["x2"])

        for idx in remove_list:
            if (
                idx["x1"] > valid_box_boundaries[0] - width_threshold
                and idx["x2"] < valid_box_boundaries[1] + width_threshold
            ):
                boxes.remove(idx)

    # 检测列并获取相关信息
    column_count, column_first_indices, left_edges, layout_result = detect_columns(
        boxes, width_threshold
    )
    if column_count is None:
        return empty_result

    # 存储所有分栏边界
    column_boundaries = [0] * 22
    for idx in range(8):
        if idx % 2 == 0:
            column_boundaries[idx] = MAX_INIT_BOUNDARY
        else:
            column_boundaries[idx] = MIN_INIT_BOUNDARY

    # 文档的分割高度位置
    split_heights = []

    for idx in range(len(boxes)):
        label = -1
        for w in range(column_count):
            if boxes[idx]["x1"] - left_edges[column_first_indices[w]] < width_threshold:
                label = w
                break
        if label == -1:
            print("error: label invalid")
            return empty_result
        boxes[idx]["lb"] = label

    margin_threshold = 1e-5
    for idx in range(len(boxes)):
        label = boxes[idx]["lb"]
        column_boundaries[label * 2] = min(
            boxes[idx]["x1"], column_boundaries[label * 2]
        )
        column_boundaries[label * 2 + 1] = max(
            boxes[idx]["x2"], column_boundaries[label * 2 + 1]
        )
        split_heights.append(boxes[idx]["y1"] + boxes[idx]["lb"] + margin_threshold)
        split_heights.append(boxes[idx]["y2"] + boxes[idx]["lb"] - margin_threshold)
    split_heights = list(dict.fromkeys(split_heights))
    split_heights.sort()

    # 合并
    split_heights = merge_close_coordinates(split_heights, height_threshold)

    cols = column_boundaries[: 2 * layout_result["cutType"]]
    # 过滤掉无效列座标
    cols = [col for col in cols if 0 <= col <= 1]
    cols.sort()

    layout_result["height"] = split_heights
    layout_result["column"] = cols

    if layout_result["cutType"] == 3:
        layout_result["cutType"] = 6

    """
    {"column": [0.0332, 0.3436, 0.3448, 0.6476, 0.6552, 0.9642], "height": [0.2733, 0.4244, 0.7822, 0.9249, 1.4962, 1.6507, 2.0389, 2.1161, 2.6025, 2.9086], "cutType": 6}
    """
    return layout_result


@app.get("/getsplitlines")
def do_get(imgurl: str):
    """
    Args:
        imgurl: http://ip:8888/getsplitlines?imgurl=https://somedomain/someimage.jpg
    """

    try:
        return run(imgurl)
    except Exception as e:
        print(f"do_get error: {e}")
        return {"code": 21200, "column": []}


def results_to_json(results, sz):
    boxes = []
    for result in results.xyxy:
        for pred in result:
            x1, y1, x2, y2 = pred[:4].tolist()
            ad = {
                "x1": x1 / sz[0],
                "y1": y1 / sz[1],
                "x2": x2 / sz[0],
                "y2": y2 / sz[1],
            }
            boxes.append(ad)
    return boxes


def loadmodel():
    global model_ready
    # use only  cpu. time*= 3~10; still <0.1s / image for inferencing
    model_ready["sp"] = torch.hub.load(
        "ultralytics/yolov5",
        "custom",
        path="./model/sp41_e84.pt",
        source="local",
        device="cpu",
    )


if __name__ == "__main__":
    app_str = "server_minimal_sp:app"
    uvicorn.run(app_str, host="0.0.0.0", port=8888, reload=True, workers=1)
