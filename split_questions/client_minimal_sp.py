import requests
import os
from PIL import Image, ImageDraw


boxlist = ["x1", "y1", "x2", "y2"]


def show_labelpil(imp, js):
    img_path = os.path.join(imp)

    im = Image.open(img_path)
    img = ImageDraw.Draw(im)
    ww, hh = im.size
    for q in js["column"]:
        ps = [int(q * ww), 1, int(ww * q), hh - 1]
        print("client_minimal_sp_column:", ps)
        img.line(ps, fill="red", width=1)

    for q in js["height"]:
        print(q)
        hn = int(js["column"][int(q) * 2] * ww + 1)
        hn2 = int(js["column"][int(q) * 2 + 1] * ww)
        ps = [hn, int(q % 1 * hh), hn2, int(q % 1 * hh)]
        print("client_minimal_sp_height:", ps)
        img.line(ps, fill="red", width=1)

    im.save(os.path.join("qqq__" + imp))


def download(url, file):
    r = requests.get(url)
    if r.status_code != 200:
        print("r.status_code!=200", file)
    open(file, "wb").write(r.content)


def rget(pp, url, fn):
    s = pp + url
    print("pp+url:", s)
    js = requests.get(s).json()
    print("requests_getjson:", js)

    download(url, fn)
    show_labelpil(fn, js)


if __name__ == "__main__":
    dol = [
        [
            "https://yuntisyscdn.bookln.cn/server/resource/pdf/245144377/17311492/3092068/low_jpg/17311492_C60FDACD81DC754D6E21D53CDFA91340.jpg?_width=1489&_height=2074"
        ]
    ]
    pp = "http://127.0.0.1:9003/getsplitlines?imgurl="
    for q in dol:
        rget(pp, q[0], q[1])
